import asyncio
import os
import sys
from dotenv import load_dotenv

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2

load_dotenv()

# Set optimal interrupt detection environment variables for real-time demo
os.environ.setdefault('VAD_THRESHOLD', '0.05')  # Sensitive voice detection
os.environ.setdefault('VAD_METHOD', 'webrtcvad')  # Use WebRTC VAD for best performance
os.environ.setdefault('WEBRTC_AGGRESSIVENESS', '3')  # Maximum aggressiveness
os.environ.setdefault('TTS_INTERRUPT_COOLDOWN_SECONDS', '0.0')  # Immediate interrupts
os.environ.setdefault('SIMPLE_INTERRUPT_CONFIRMATION', 'true')  # Simplified confirmation for demo

async def select_input_device():
    import sounddevice as sd
    devices = sd.query_devices()
    input_devices = [(i, d) for i, d in enumerate(devices) if d['max_input_channels'] > 0]
    print("Available input devices:")
    for idx, dev in input_devices:
        print(f"  [{idx}] {dev['name']} (inputs: {dev['max_input_channels']})")
    while True:
        try:
            device_index = int(input("Enter the device index for your microphone: ").strip())
            if any(idx == device_index for idx, _ in input_devices):
                return device_index
            else:
                print("Invalid index. Please select from the list above.")
        except Exception:
            print("Please enter a valid integer index.")

async def record_audio_wav(duration_sec=3, sample_rate=16000, device_index=None):
    import sounddevice as sd
    import wave
    print(f"[MIC] Recording {duration_sec} seconds of audio...")
    audio = sd.rec(int(duration_sec * sample_rate), samplerate=sample_rate, channels=1, dtype='int16', device=device_index)
    sd.wait()
    audio_bytes = audio.tobytes()
    with wave.open('last_recorded.wav', 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_bytes)
    print(f"[INFO] Saved your recorded audio to: last_recorded.wav")
    return 'last_recorded.wav'

async def capture_real_time_audio(session_manager, session_id, device_index=None, duration_sec=5):
    """
    Capture real-time audio from microphone and store it in memory for processing.
    Returns the path to the captured audio file.
    """
    import sounddevice as sd
    import wave
    import tempfile
    import time

    sample_rate = 16000
    print(f"🎤 [REAL-TIME] Listening for {duration_sec} seconds...")
    print("🗣️  Speak now!")

    # Record audio from microphone
    audio = sd.rec(int(duration_sec * sample_rate), samplerate=sample_rate, channels=1, dtype='int16', device=device_index)
    sd.wait()

    # Create temporary file for the audio
    timestamp = int(time.time())
    temp_audio_path = f"temp_audio_{timestamp}.wav"

    # Save audio to file
    audio_bytes = audio.tobytes()
    with wave.open(temp_audio_path, 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_bytes)

    print(f"✅ [REAL-TIME] Audio captured and saved to: {temp_audio_path}")
    return temp_audio_path

async def run_real_time_voice_agent():
    """
    Run a real-time voice agent that continuously listens for user input,
    processes it through the voice pipeline, and supports interruptions.
    """
    print("🎯 Real-Time Voice Agent Demo")
    print("=" * 50)

    # Select microphone device
    device_index = await select_input_device()

    # Initialize SessionManagerV2
    session_manager = SessionManagerV2()
    workflow_name = 'banking_workflow_v2.json'
    user_id = 'realtime_voice_user'

    try:
        # Create a new session
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created: {session_id}")

        # Retrieve the memory manager for the session
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]

        print("\n🎤 Starting real-time voice conversation...")
        print("💡 Tips:")
        print("   - Speak clearly into your microphone")
        print("   - You can interrupt the AI during TTS playback")
        print("   - Type 'quit' to exit")
        print()

        conversation_count = 0

        while True:
            conversation_count += 1
            print(f"\n--- Conversation Turn {conversation_count} ---")

            try:
                # Capture real-time audio from user
                print("🎤 Listening... (5 seconds)")
                print("   💬 Say something like: 'What's my account balance?' or 'Transfer money'")
                audio_path = await capture_real_time_audio(session_manager, session_id, device_index, duration_sec=5)

                # Set the USER audio file path in contextual memory (separate from TTS output)
                print(f"[DEBUG] Setting user_audio_path in memory to: {audio_path}")
                await memory_manager.set("contextual", "user_audio_path", audio_path)
                print(f"✅ User audio captured and stored in memory")

                # Retrieve the orchestrator (OrchestratorV3) for the session
                orchestrator = await session_manager.initialize_orchestrator(session_id)

                # Run the orchestrator for this conversation turn
                print("🤖 Processing your request through the voice pipeline...")
                print("   📝 STT: Converting speech to text...")
                print("   🧠 Intent: Analyzing your request...")
                print("   ⚙️  Agent: Processing your request...")
                print("   🔊 TTS: Generating response (you can interrupt by speaking)...")

                result = await orchestrator.run()

                if result.get('status') == 'completed':
                    print("✅ Request processed successfully!")
                elif result.get('status') == 'interrupted':
                    print("⚡ Conversation was interrupted - continuing...")
                else:
                    print(f"⚠️  Processing completed with status: {result.get('status', 'unknown')}")
                    if result.get('reason'):
                        print(f"   Reason: {result.get('reason')}")

            except Exception as e:
                print(f"❌ Error in conversation turn: {e}")
                print("   Continuing to next turn...")

            finally:
                # Clean up temporary audio file
                try:
                    if 'audio_path' in locals():
                        os.unlink(audio_path)
                except:
                    pass  # Ignore cleanup errors

            # Ask if user wants to continue
            print("\n🔄 Continue conversation?")
            print("   Press Enter to continue, or type 'quit' to exit")
            user_input = input().strip().lower()
            if user_input in ['quit', 'exit', 'q']:
                print("👋 Ending conversation...")
                break

    finally:
        # Clean up the session
        await session_manager.cleanup_session(session_id, reason="realtime_demo_complete")
        print("\n✅ Session cleaned up. Goodbye!")

async def run_audio_interrupt_orchestrator():
    print("Choose input method:")
    print("1. Real-time microphone input (default)")
    print("2. Provide path to a single audio file")
    print("3. Loop through /data/filler_words/user_conversation_part_1.mp3, 2, 3")
    choice = input("Enter your choice (1, 2, or 3): ").strip()
    audio_paths = []
    device_index = None
    if choice == "2":
        path = input("Enter path to audio file (.wav or .mp3): ").strip()
        if not os.path.exists(path):
            print("File not found.")
            return
        audio_paths = [path]
    elif choice == "3":
        base = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/filler_words'))
        audio_paths = [
            os.path.join(base, f"user_conversation_part_{i}.mp3") for i in range(1, 4)
        ]
        for path in audio_paths:
            if not os.path.exists(path):
                print(f"File not found: {path}")
                return
    elif choice == "1" or choice == "":
        # Real-time mic input (default)
        device_index = await select_input_device()
    else:
        print("Invalid choice.")
        return

    # Initialize SessionManagerV2
    session_manager = SessionManagerV2()
    workflow_name = 'banking_workflow_v2.json'
    user_id = 'realtime_voice_user'

    try:
        # Create a new session
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created: {session_id}")

        # Retrieve the memory manager for the session
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]

        if audio_paths:
            # Run orchestrator once for greeting (no audio_path)
            orchestrator = await session_manager.initialize_orchestrator(session_id)
            print("\n[ORCHESTRATOR] Running for greeting...")
            result = await orchestrator.run()
            print(f"[ORCHESTRATOR RESULT] {result}")
            print("[INFO] Greeting played. Now running user audio files.")
            # Loop through provided audio files for user input
            for idx, audio_path in enumerate(audio_paths):
                print(f"\n[ORCHESTRATOR] Running with audio file {idx+1}/{len(audio_paths)}: {audio_path}")
                print(f"[DEBUG] Setting audio_path in memory to: {audio_path}")
                await memory_manager.set("contextual", "audio_path", audio_path)
                # Do NOT re-initialize orchestrator; use the same instance
                result = await orchestrator.run()
                print(f"[ORCHESTRATOR RESULT] {result}")
                print("[INFO] If you want to test interrupts, speak during TTS playback.")
                print("[PIPELINE COMPLETE]")
        else:
            # Real-time mic input loop (original behavior)
            print("\n🎤 Starting real-time voice conversation...")
            print("💡 Tips:")
            print("   - Speak clearly into your microphone")
            print("   - You can interrupt the AI during TTS playback")
            print("   - Type 'quit' to exit")
            print()

            # Initialize orchestrator once
            orchestrator = await session_manager.initialize_orchestrator(session_id)

            # First run: Greeting state (no user audio needed)
            print("\n--- Initial Greeting ---")
            print("🤖 AI is greeting you...")
            result = await orchestrator.run()
            if result.get('status') == 'completed':
                print("✅ Greeting completed!")
            else:
                print(f"⚠️  Greeting completed with status: {result.get('status', 'unknown')}")
                if result.get('reason'):
                    print(f"   Reason: {result.get('reason')}")

            conversation_count = 0
            while True:
                conversation_count += 1
                print(f"\n--- Conversation Turn {conversation_count} ---")
                try:
                    # Capture real-time audio from user
                    print("🎤 Listening... (5 seconds)")
                    print("   💬 Say something like: 'What's my account balance?' or 'Transfer money'")
                    audio_path = await record_audio_wav(device_index=device_index)
                    await memory_manager.set("contextual", "audio_path", audio_path)

                    print("🤖 Processing your request through the voice pipeline...")
                    result = await orchestrator.run()
                    if result.get('status') == 'completed':
                        print("✅ Request processed successfully!")
                    elif result.get('status') == 'interrupted':
                        print("⚡ Conversation was interrupted - continuing...")
                    else:
                        print(f"⚠️  Processing completed with status: {result.get('status', 'unknown')}")
                        if result.get('reason'):
                            print(f"   Reason: {result.get('reason')}")
                except Exception as e:
                    print(f"❌ Error in conversation turn: {e}")
                    print("   Continuing to next turn...")
                finally:
                    try:
                        if 'audio_path' in locals():
                            os.unlink(audio_path)
                    except:
                        pass
                print("\n🔄 Continue conversation?")
                print("   Press Enter to continue, or type 'quit' to exit")
                user_input = input().strip().lower()
                if user_input in ['quit', 'exit', 'q']:
                    print("👋 Ending conversation...")
                    break
    finally:
        await session_manager.cleanup_session(session_id, reason="realtime_demo_complete")
        print("\n✅ Session cleaned up. Goodbye!")

if __name__ == "__main__":
    print("🚀 Starting Real-Time Voice Agent Demo")
    print("This demo creates a production-ready voice assistant with:")
    print("  ✅ Real-time microphone input")
    print("  ✅ Complete STT → Intent → Agent → TTS pipeline")
    print("  ✅ Voice interruption support during TTS")
    print("  ✅ Continuous conversation loop")
    print()
    asyncio.run(run_audio_interrupt_orchestrator())