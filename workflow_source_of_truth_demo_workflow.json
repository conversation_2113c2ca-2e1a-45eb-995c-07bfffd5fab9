{"id": "demo_workflow", "name": "Demo Workflow", "version": "1.0", "states": {"Greeting": {"type": "input", "layer2_id": "l2_greeting_banking_system_v2", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["tts_audio_path", "latencyTTS"]}, "Goodbye": {"type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["tts_audio_path", "latencyTTS"]}}, "rules": [{"from": "Greeting", "condition": "true", "to": "Goodbye"}], "allowed_actions": ["Greet user", "Say goodbye"], "prohibited_actions": ["Do not process real transactions"], "allowed_tools": ["TTS"]}