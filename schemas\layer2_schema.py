from typing import Optional, Union
from pydantic import BaseModel, Field

# --- Input/Output Schema Definitions for Pipeline States V2 ---

class STTInputSchema(BaseModel):
    """Schema for STT State input validation"""
    # New field for user input audio (preferred)
    user_audio_path: Optional[Union[str, bytes]] = Field(None, description="Path to user input audio file or audio bytes")
    # Legacy field for backward compatibility
    audio_path: Optional[Union[str, bytes]] = Field(None, description="Legacy: Path to audio file or audio bytes (use user_audio_path instead)")

    def get_audio_path(self) -> Optional[Union[str, bytes]]:
        """Get the audio path, preferring user_audio_path over legacy audio_path"""
        return self.user_audio_path or self.audio_path

class STTOutputSchema(BaseModel):
    """Schema for STT State output validation"""
    text: str = Field(..., description="Transcribed text from audio")
    latencySTT: int = Field(..., description="Processing latency in milliseconds")

class PreProcessingInputSchema(BaseModel):
    """Schema for PreProcessing State input validation"""
    transcript: str = Field(..., min_length=1, description="Raw transcript text to process")

class PreProcessingOutputSchema(BaseModel):
    """Schema for PreProcessing State output validation"""
    clean_text: str = Field(..., description="Cleaned and processed text")
    intent: str = Field(..., description="Detected user intent")
    emotion: Optional[str] = Field(None, description="Detected emotion")
    gender: Optional[str] = Field(None, description="Detected gender")
    latencyPreprocessing: int = Field(..., description="Processing latency in milliseconds")

class ProcessingInputSchema(BaseModel):
    """Schema for Processing State input validation"""
    clean_text: str = Field(..., min_length=1, description="Clean text to process")
    intent: str = Field(..., description="User intent for processing")

class ProcessingOutputSchema(BaseModel):
    """Schema for Processing State output validation"""
    llm_answer: str = Field(..., description="LLM generated response")
    latencyProcessing: int = Field(..., description="Processing latency in milliseconds")

class FillerInputSchema(BaseModel):
    """Schema for Filler State input validation"""
    filler_text: Optional[str] = Field(None, description="Optional filler text to use")

class FillerOutputSchema(BaseModel):
    """Schema for Filler State output validation"""
    # New field for filler TTS output audio (preferred)
    tts_audio_path: str = Field(..., description="Path to generated filler TTS audio")
    filler_text: str = Field(..., description="Text used for filler generation")
    # Legacy field for backward compatibility (optional)
    audio_path: Optional[str] = Field(None, description="Legacy: Path to generated filler audio (use tts_audio_path instead)")

class TTSInputSchema(BaseModel):
    """Schema for TTS State input validation"""
    text: str = Field(..., min_length=1, description="Text to convert to speech")
    emotion: str = Field("neutral", description="Emotion for speech synthesis")
    gender: str = Field("male", description="Gender for voice selection")

class TTSOutputSchema(BaseModel):
    """Schema for TTS State output validation"""
    # New field for TTS output audio (preferred)
    tts_audio_path: str = Field(..., description="Path to generated TTS audio file")
    latencyTTS: int = Field(..., description="Processing latency in milliseconds")
    # Legacy field for backward compatibility (optional)
    audio_path: Optional[str] = Field(None, description="Legacy: Path to generated audio file (use tts_audio_path instead)")