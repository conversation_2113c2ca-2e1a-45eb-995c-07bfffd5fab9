{"id": "stt_test_workflow", "name": "STT Agent Test Flow", "version": "1.0", "states": {"state_greeting": {"type": "input", "layer2_id": "l2_ai_greeting", "allowed_tools": ["STT"], "expected_input": ["user_audio_path"], "expected_output": ["intent", "llm_answer", "tts_audio_path"]}, "state_2": {"type": "input", "layer2_id": "l2_state_2", "allowed_tools": ["STT"], "expected_input": ["user_audio_path"], "expected_output": ["intent", "llm_answer", "tts_audio_path"]}, "state_3": {"type": "input", "layer2_id": "l2_state_3", "allowed_tools": ["STT"], "expected_input": ["user_audio_path"], "expected_output": ["intent", "llm_answer", "tts_audio_path"]}}, "rules": [{"from": "state_greeting", "condition": "always", "to": "state_2"}, {"from": "state_2", "condition": "always", "to": "state_3"}], "allowed_actions": [], "prohibited_actions": [], "allowed_tools": ["STT"]}