{"id": "banking_customer_service", "name": "Banking Customer Service", "version": "1.0", "states": {"Greeting": {"type": "input", "layer2_id": "l2_greeting_banking_system_v2", "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "expected_input": [], "expected_output": ["tts_audio_path", "latencyTTS"]}, "Inquiry": {"type": "inform", "layer2_id": "l2_inquiry_banking_system_v2", "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "expected_input": ["text"], "expected_output": ["tts_audio_path", "latencyTTS"]}, "CheckBalance": {"type": "inform", "layer2_id": "l2_check_balance_banking_system_v2", "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "expected_input": ["account_id"], "expected_output": ["tts_audio_path", "latencyTTS"]}, "TransferFunds": {"type": "transaction", "layer2_id": "l2_transfer_funds_banking_system_v2", "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["tts_audio_path", "latencyTTS"]}, "exchangeRate": {"type": "transaction", "layer2_id": "l2_exchange_rate_banking_system_v2", "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["tts_audio_path", "latencyTTS"]}, "Goodbye": {"type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["tts_audio_path", "latencyTTS"]}}, "rules": [{"from": "Greeting", "condition": "true", "to": "Inquiry"}, {"from": "Inquiry", "condition": "intent == 'account_balance'", "to": "CheckBalance"}, {"from": "Inquiry", "condition": "intent == 'fund_transfer'", "to": "TransferFunds"}, {"from": "Inquiry", "condition": "intent == 'exchange_rate'", "to": "exchangeRate"}, {"from": "Inquiry", "condition": "intent == 'goodbye'", "to": "Goodbye"}], "allowed_actions": ["Check account balance", "Transfer funds", "Apply for loan", "Report lost card", "Update personal information"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification", "Do not disclose sensitive account details"], "allowed_tools": ["CACHE", "TRANSACTION_API", "STT", "DB_QUERY", "LLM", "TTS"]}