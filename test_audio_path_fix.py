#!/usr/bin/env python3
"""
Test script to verify that the audio path separation fix is working correctly.
This script tests that user input audio paths and TTS output audio paths are stored separately.
"""

import asyncio
import os
import sys
import tempfile
import wave

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2
from core.memory.memory_manager import MemoryManager

async def create_test_audio_file(filename="test_audio.wav", duration_sec=1):
    """Create a simple test audio file."""
    import numpy as np
    
    sample_rate = 16000
    samples = int(duration_sec * sample_rate)
    
    # Generate a simple sine wave
    frequency = 440  # A4 note
    t = np.linspace(0, duration_sec, samples, False)
    audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
    
    # Convert to 16-bit integers
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # Save to WAV file
    with wave.open(filename, 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_data.tobytes())
    
    return filename

async def test_audio_path_separation():
    """Test that user audio and TTS audio paths are stored separately."""
    print("🧪 Testing Audio Path Separation Fix")
    print("=" * 50)
    
    # Create test audio files
    user_audio_file = await create_test_audio_file("test_user_audio.wav")
    tts_audio_file = await create_test_audio_file("test_tts_audio.wav")
    
    try:
        # Initialize SessionManagerV2
        session_manager = SessionManagerV2()
        workflow_name = 'banking_workflow_v2.json'
        user_id = 'test_audio_separation_user'

        # Create a new session
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created: {session_id}")

        # Get memory manager
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]
        
        # Test 1: Store user audio path
        print("\n📝 Test 1: Storing user audio path")
        await memory_manager.set("contextual", "user_audio_path", user_audio_file)
        # Use the contextual memory layer directly for retrieval
        stored_user_audio = await memory_manager.contextual.get("user_audio_path")
        print(f"   Stored user audio: {stored_user_audio}")
        assert stored_user_audio == user_audio_file, "User audio path not stored correctly"
        print("   ✅ User audio path stored correctly")
        
        # Test 2: Store TTS audio path (simulating TTS agent output)
        print("\n📝 Test 2: Storing TTS audio path")
        await memory_manager.set("contextual", "tts_audio_path", tts_audio_file)
        stored_tts_audio = await memory_manager.contextual.get("tts_audio_path")
        print(f"   Stored TTS audio: {stored_tts_audio}")
        assert stored_tts_audio == tts_audio_file, "TTS audio path not stored correctly"
        print("   ✅ TTS audio path stored correctly")
        
        # Test 3: Verify user audio path is not overwritten
        print("\n📝 Test 3: Verifying user audio path is preserved")
        user_audio_after_tts = await memory_manager.contextual.get("user_audio_path")
        print(f"   User audio after TTS: {user_audio_after_tts}")
        assert user_audio_after_tts == user_audio_file, "User audio path was overwritten!"
        print("   ✅ User audio path preserved after TTS storage")
        
        # Test 4: Test orchestrator retrieval logic
        print("\n📝 Test 4: Testing orchestrator retrieval logic")

        # Simulate orchestrator retrieving user audio for STT
        retrieved_user_audio = await memory_manager.contextual.get("user_audio_path")
        print(f"   Retrieved for STT: {retrieved_user_audio}")
        assert retrieved_user_audio == user_audio_file, "Orchestrator retrieved wrong audio path"
        print("   ✅ Orchestrator retrieves correct user audio path")
        
        # Test 5: Test that old audio_path key doesn't interfere
        print("\n📝 Test 5: Testing legacy audio_path key handling")
        
        # Store something in the old key
        await memory_manager.set("contextual", "audio_path", "legacy_audio.wav")
        
        # Verify user_audio_path is still correct
        user_audio_final = await memory_manager.get("contextual", "user_audio_path")
        assert user_audio_final == user_audio_file, "Legacy key interfered with user audio path"
        print("   ✅ Legacy audio_path key doesn't interfere")
        
        # Test 6: Test schema validation
        print("\n📝 Test 6: Testing schema validation")
        from schemas.layer2_schema import STTInputSchema, TTSOutputSchema

        # Test STTInputSchema with new field
        stt_input = STTInputSchema(user_audio_path=user_audio_file)
        assert stt_input.get_audio_path() == user_audio_file, "STTInputSchema new field failed"
        print("   ✅ STTInputSchema with user_audio_path works")

        # Test STTInputSchema with legacy field
        stt_input_legacy = STTInputSchema(audio_path=user_audio_file)
        assert stt_input_legacy.get_audio_path() == user_audio_file, "STTInputSchema legacy field failed"
        print("   ✅ STTInputSchema with legacy audio_path works")

        # Test TTSOutputSchema with new field
        tts_output = TTSOutputSchema(tts_audio_path=tts_audio_file, latencyTTS=100)
        assert tts_output.tts_audio_path == tts_audio_file, "TTSOutputSchema new field failed"
        print("   ✅ TTSOutputSchema with tts_audio_path works")

        print("\n🎉 All tests passed! Audio path separation is working correctly.")
        print("\n📊 Summary:")
        print(f"   - User audio path: {user_audio_final}")
        print(f"   - TTS audio path: {stored_tts_audio}")
        print("   - Schema validation: ✅")
        print("   - Paths are properly separated ✅")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
    
    finally:
        # Cleanup
        try:
            await session_manager.cleanup_session(session_id, reason="test_complete")
            os.unlink(user_audio_file)
            os.unlink(tts_audio_file)
            print("\n🧹 Cleanup completed")
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_audio_path_separation())
