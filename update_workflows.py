#!/usr/bin/env python3
"""
<PERSON>ript to update all workflow JSON files to use the new audio path keys.
"""

import json
import os
import glob

def update_workflow_file(file_path):
    """Update a single workflow file to use new audio path keys."""
    print(f"Updating {file_path}...")
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # Track if any changes were made
    changes_made = False
    
    # Update pipeline steps
    if 'pipeline' in data:
        for step in data['pipeline']:
            # Update STT input: audio_path -> user_audio_path
            if step.get('step') == 'stt' and 'input' in step:
                if 'audio_path' in step['input']:
                    step['input']['user_audio_path'] = step['input'].pop('audio_path')
                    changes_made = True
                    print(f"  - Updated STT input: audio_path -> user_audio_path")
            
            # Update STT output: remove audio_path (not needed)
            if step.get('step') == 'stt' and 'output' in step:
                if 'audio_path' in step['output']:
                    step['output'].pop('audio_path')
                    changes_made = True
                    print(f"  - Removed STT output: audio_path")
            
            # Update TTS output: audio_path -> tts_audio_path
            if step.get('step') in ['tts', 'filler_tts'] and 'output' in step:
                if 'audio_path' in step['output']:
                    step['output']['tts_audio_path'] = step['output'].pop('audio_path')
                    changes_made = True
                    print(f"  - Updated {step['step']} output: audio_path -> tts_audio_path")
    
    # Update states (for older workflow format)
    if 'states' in data:
        for state_name, state_data in data['states'].items():
            # Update expected_input
            if 'expected_input' in state_data:
                if 'audio_path' in state_data['expected_input']:
                    idx = state_data['expected_input'].index('audio_path')
                    state_data['expected_input'][idx] = 'user_audio_path'
                    changes_made = True
                    print(f"  - Updated state {state_name} expected_input: audio_path -> user_audio_path")
            
            # Update expected_output
            if 'expected_output' in state_data:
                if 'audio_path' in state_data['expected_output']:
                    idx = state_data['expected_output'].index('audio_path')
                    state_data['expected_output'][idx] = 'tts_audio_path'
                    changes_made = True
                    print(f"  - Updated state {state_name} expected_output: audio_path -> tts_audio_path")
    
    # Save the updated file if changes were made
    if changes_made:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=4)
        print(f"  ✅ Saved changes to {file_path}")
    else:
        print(f"  ℹ️  No changes needed for {file_path}")
    
    return changes_made

def main():
    """Update all workflow files."""
    print("🔄 Updating workflow files for audio path separation...")
    
    # Find all JSON files in workflows directory
    workflow_files = glob.glob("workflows/*.json")
    
    # Also check for JSON files in the root directory
    root_workflow_files = glob.glob("workflow_*.json")
    
    all_files = workflow_files + root_workflow_files
    
    if not all_files:
        print("❌ No workflow files found!")
        return
    
    total_updated = 0
    
    for file_path in all_files:
        try:
            if update_workflow_file(file_path):
                total_updated += 1
        except Exception as e:
            print(f"❌ Error updating {file_path}: {e}")
    
    print(f"\n🎉 Update complete! Updated {total_updated} out of {len(all_files)} files.")

if __name__ == "__main__":
    main()
