import asyncio
import os
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
import aiofiles  # Add this import at the top

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.logging.exceptions import Configuration<PERSON>rror, WorkflowError, Layer2Error
from core.logging.logger import Logger, StateOutput as LoggerStateOutput
from core.logging.logger_config import get_module_logger
from core.orchestrator.agent_registry import AgentRegistry, REGISTRY_REDIS_KEY
from core.memory.redis_context import RedisClient
from agents.filler.filler_tts_agent import FillerTTSAgent
from agents.processing.preprocessing_agent import PreprocessingAgent
from agents.processing.processing_agent import ProcessingAgent
from agents.tts.tts_agent import TTSAgent
from agents.stt.stt_agent import STTAgent
from agents.tts.tts_openai import TTSAgentOpenAI
from schemas.agent_metadata import AgentMetadata

from core.state_manager.loaders.WorkflowSchemaLoader import WorkflowSchemaLoader
from core.state_manager.loaders.Layer2SchemaLoader import Layer2SchemaLoader
from schemas.workflow_schema import WorkflowWrapper, State, Layer2, PipelineStep
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from asteval import Interpreter
from core.memory.memory_manager import MemoryManager
from core.state_manager.state_output import (
    AbstractPipelineState, STTState, PreProcessingState, ProcessingState, FillerState, TTSState, InterruptState
)

from core.config.interrupt_config import get_interrupt_config
from core.interruption.action_reversibility import ActionReversibilityDetector, detect_action_reversibility, get_interrupt_message
from core.interruption.interrupt_manager import InterruptManager
from core.interruption.tts_interrupt_monitor import TTSInterruptMonitor


logger = get_module_logger("state_manager")

# Add config loader
async def load_config():
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config_path = os.path.join(base_dir, "workflows", "state_manager_config.json")
    try:
        async with aiofiles.open(config_path, 'r') as f:
            content = await f.read()
            return json.loads(content)
    except Exception as e:
        logger.error(
            "Could not load config file",
            action="load_config",
            input_data={"config_path": config_path},
            reason=str(e),
            layer="configuration"
        )
        raise ConfigurationError(f"Failed to load state manager configuration: {e}")

class StateManager:
    """
    Main State Manager - the brain of the agent's execution loop.
    
    This class manages:
    - Workflow state transitions based on the provided schema format
    - Execution of Layer2 pipelines
    - Memory management 
    - Agent coordination (TODO: integrate with MCPAgentRegistry)
    """
    
    def __init__(self, workflow_name: str, session_id: str, user_id: Optional[str] = None, _internal_init: bool = False):
        """
        Initialize the StateManager with basic attributes.
        Note: This constructor only sets up basic attributes. Use create() for full async initialization.
        
        Args:
            workflow_name: Name of the workflow to execute
            session_id: Unique session identifier
            user_id: Optional user identifier
            _internal_init: Internal flag to prevent direct instantiation
        
        Raises:
            RuntimeError: If initialized directly instead of through create()
        """
        if not _internal_init:
            raise RuntimeError(
                "StateManager cannot be initialized directly. Use StateManager.create() instead."
            )
            
        self.workflow_name = workflow_name
        self.user_id = user_id
        self.session_id = session_id
        self.logger = get_module_logger("state_manager")
        self.base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        # Initialize attributes that will be set during async initialization
        self.config = None
        self.workflow_file_path = None
        self.workflow = None  # TODO: WorkflowWrapper type
        self.layer2_map = {}  # TODO: Dict[str, Layer2] type
        self.memory_manager = None
        self.execution_history = []
        self.is_running = True
        self.current_workflow_state_id = None
        self.current_pipeline_step_id = None
        self.redis_client = RedisClient()
        self.agent_registry = AgentRegistry(self.redis_client)
        self.pipeline_state_map = {}
        self.interrupt_config = None  # Will be set after workflow loading

        # Simple interrupt handling
        self.current_tts_playback = None
        self.interrupt_in_progress = False
        self.skip_interrupt_monitoring = False  # Flag to skip interrupt monitoring for acknowledgment TTS


    @classmethod
    async def create(cls, workflow_name: str, session_id: str, user_id: Optional[str] = None):
        """
        Async factory method to create and fully initialize a StateManager instance.
        
        Args:
            workflow_name: Name of the workflow to execute
            session_id: Unique session identifier
            user_id: Optional user identifier
            
        Returns:
            StateManager: Fully initialized StateManager instance
            
        Raises:
            ConfigurationError: If configuration files cannot be loaded
            WorkflowError: If the workflow cannot be loaded or is invalid
            Layer2Error: If a Layer2 definition cannot be loaded or is invalid
        """
        # Pass internal flag to allow initialization
        instance = cls(workflow_name, session_id, user_id, _internal_init=True)
        await instance._async_initialize()
        return instance

    async def _async_initialize(self):
        """
        Perform async initialization of the StateManager.
        """
        logger.info(
            "Initializing StateManager",
            action="initialize",
            input_data={
                "workflow": self.workflow_name,
                "session_id": self.session_id,
                "user_id": self.user_id
            },
            layer="state_management"
        )
        
        # Load configuration
        try:
            self.config = await load_config()
            workflow_states_dir = self.config["paths"]["workflow_states_dir"]
            self.workflow_file_path = os.path.join(self.base_dir, workflow_states_dir, self.workflow_name)
        except ConfigurationError as e:
            logger.error(
                "Configuration error during StateManager initialization",
                action="initialize",
                reason=str(e),
                layer="state_management"
            )
            raise

        # Initialize memory manager
        self.memory_manager = MemoryManager(self.session_id, self.user_id)

        # Initialize interrupt management components (will be updated after workflow loading)
        self.interrupt_manager = None
        self.tts_monitor = None

        # Initialize agent registry
        await self.agent_registry.initialize()
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="stt_agent", version="1.0", description="Speech-to-Text Agent"), agentObject=STTAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="tts_agent", version="1.0", description="Text-to-Speech Agent"), agentObject=TTSAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="filler_tts_agent", version="1.0", description="Filler Text-to-Speech Agent"), agentObject=FillerTTSAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="preprocessing_agent", version="1.0", description="Preprocessing Agent"), agentObject=PreprocessingAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="processing_agent", version="1.0", description="Processing Agent"), agentObject=ProcessingAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="tts_agent_openAI", version="1.0", description="Text-to-Speech Agent OpenAI"), agentObject=TTSAgentOpenAI(self.session_id, self.current_workflow_state_id))

        self.pipeline_state_map = {
            "stt_process": STTState,
            "preprocessing_process": PreProcessingState,
            "processing_process": ProcessingState,
            "filler_tts_process": FillerState,
            "tts_process": TTSState,
            "interrupt_process": InterruptState,
        }

        # Update logger context with session information
        logger.update_context(session_id=self.session_id, state_id="initialization")

        try:
            await self._load_workflow_async()

            # Load interrupt configuration with workflow overrides
            self.interrupt_config = get_interrupt_config(self.raw_workflow_dict)

            # Initialize interrupt management components with workflow-aware configuration
            self.interrupt_manager = InterruptManager(self.session_id, self.memory_manager, self.interrupt_config)
            self.tts_monitor = TTSInterruptMonitor(self.session_id, self.memory_manager, self.interrupt_config, self.agent_registry)

            await self.check_and_persist_workflow_summary()
            await self._load_all_layer2_async()
            pass

            # Set initial state
            if self.workflow and hasattr(self.workflow.workflow, 'start'):
                self.current_workflow_state_id = self.workflow.workflow.start
                logger.update_context(state_id=self.current_workflow_state_id)
                logger.info(
                    "StateManager initialization completed",
                    action="initialize",
                    output_data={"initial_state": self.current_workflow_state_id},
                    layer="state_management"
                )
                
                # Set initial Layer2 step
                if self.current_workflow_state_id:
                    current_state = self.get_state(self.current_workflow_state_id)
                    if current_state and hasattr(current_state, 'layer2_id'):
                        layer2 = self.layer2_map.get(current_state.layer2_id)
                        if layer2 and layer2.pipeline:
                            self.current_pipeline_step_id = layer2.pipeline[0].step


        except Exception as e:
            logger.error(
                "Error initializing StateManager",
                action="initialize",
                reason=str(e),
                layer="state_management"
            )
            self.workflow = None
            raise

    async def _load_workflow_async(self):
        """
        Asynchronously load the workflow schema.
        """
        try:
            print("Loading workflow from:", self.workflow_file_path)
            # Load and store the raw workflow dict
            import json
            with open(self.workflow_file_path, 'r', encoding='utf-8') as f:
                self.raw_workflow_dict = json.load(f)
            self.workflow = await WorkflowSchemaLoader.load(self.workflow_file_path)
            if not self.workflow:
                raise Exception(f"Failed to load workflow: {self.workflow_file_path}")
        except Exception as e:
            logger.error(
                "Error loading workflow",
                action="_load_workflow_async",
                input_data={"workflow_file_path": self.workflow_file_path},
                reason=str(e),
                layer="state_management"
            )
            raise

    async def _initialize_session_metadata(self):
        """
        Initialize session metadata in contextual memory for tracking throughout the session.
        """
        from datetime import datetime
        if self.memory_manager and self.workflow:
            # Store workflow and session information
            await self.memory_manager.set("contextual", "workflow_name", self.workflow_name)
            await self.memory_manager.set("contextual", "pipeline_id", self.workflow.workflow.id)
            await self.memory_manager.set("contextual", "current_state", self.current_state_id)
            await self.memory_manager.set("contextual", "states_visited", [self.current_state_id])
            await self.memory_manager.set("contextual", "interrupts", [])
            await self.memory_manager.set("contextual", "session_start_time", datetime.now().isoformat())
            await self.memory_manager.set("contextual", "outcome", "in_progress")

            # Store complete workflow metadata for pipeline saving
            workflow_states = []
            if hasattr(self.workflow.workflow, 'states') and self.workflow.workflow.states:
                workflow_states = list(self.workflow.workflow.states.keys())

            await self.memory_manager.set("contextual", "workflow_states", workflow_states)
            await self.memory_manager.set("contextual", "workflow_description", getattr(self.workflow.workflow, 'description', f"Workflow: {self.workflow_name}"))
            await self.memory_manager.set("contextual", "workflow_version", getattr(self.workflow.workflow, 'version', "1.0"))

            logger.info(
                "Initialized session metadata in contextual memory",
                action="_initialize_session_metadata",
                output_data={
                    "workflow_name": self.workflow_name,
                    "pipeline_id": self.workflow.workflow.id,
                    "initial_state": self.current_state_id
                },
                layer="state_management"
            )

    async def _load_all_layer2_async(self):
        # Create a semaphore to limit concurrent loads
        semaphore = asyncio.Semaphore(5)  # Limit to 5 concurrent loads
        
        layer2_files_to_load = []
        for layer2_id, state in self.workflow.workflow.states.items():
            if not state.layer2_id:
                continue
            layer2_file = f"{self.config['paths']['layer2_config_dir']}/{state.layer2_id}.json"
            layer2_files_to_load.append((state.layer2_id, layer2_file, state.expected_output))

        # Create tasks with semaphore control
        async def load_with_semaphore(layer2_id, layer2_file, expected_output):
            async with semaphore:
                try:
                    return layer2_id, await Layer2SchemaLoader.load(layer2_file, expected_output)
                except Exception as e:
                    logger.error(
                        "Error loading Layer2",
                        action="_load_all_layer2_async",
                        input_data={"layer2_id": layer2_id, "layer2_file": layer2_file},
                        reason=str(e),
                        layer="state_management"
                    )
                    return layer2_id, None

        # Create all tasks
        tasks = [load_with_semaphore(l2_id, l2_file, exp_output) 
                 for l2_id, l2_file, exp_output in layer2_files_to_load]
        
        # Wait for all tasks to complete, even if some fail
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                logger.error(
                    "Task failed during Layer2 loading",
                    action="_load_all_layer2_async",
                    reason=str(result),
                    layer="state_management"
                )
                continue
                
            layer2_id, layer2_obj = result
            if layer2_obj:
                self.layer2_map[layer2_id] = layer2_obj
            else:
                logger.warning(
                    "Failed to load Layer2",
                    action="_load_all_layer2_async",
                    input_data={"layer2_id": layer2_id},
                    layer="state_management"
                )

    def get_state(self, state_id: str):
        return self.workflow.workflow.states.get(state_id)

    def get_layer2(self, layer2_id: str):
        return self.layer2_map.get(layer2_id)

    async def execute_step(self, input_data: dict) -> StateOutput:
        """
        Executes a single step of the workflow using the current state.
        """

        # Update logger context for current state
        logger.update_context(session_id=self.session_id, state_id=self.current_workflow_state_id)

        logger.info(
            "Starting state execution",
            action="execute_step",
            input_data={"current_state": self.current_workflow_state_id, "input": input_data},
            layer="state_management"
        )

        try :
            state_config = self.get_state(self.current_workflow_state_id)
            layer2_config = self.get_layer2(state_config.layer2_id)
            # TO AMMAR YASSER THESE IS HOW WE ADD IN PIPELINE STATE V2
            # pipeline_state_map = {
            #     "stt": STTState,
            #     "preprocessing": PreProcessingState,
            #     "processing": ProcessingState,
            #     "filler": FillerState,
            #     "tts": TTSState,
            # }
            # state_type = getattr(state_config, 'type', None)
            # PipelineStateClass = pipeline_state_map.get(state_type)
            # if PipelineStateClass is not None:
            #     # For STT, pass input_data; for others, pass {}
            #     if state_type == "stt":
            #         pipeline_input = input_data
            #     else:
            #         pipeline_input = {}
            #     state = PipelineStateClass(
            #         state_id=self.current_workflow_state_id,
            #         agent_registry=self.agent_registry,
            #         session_id=self.session_id
            #     )
            #     result = await state.process(pipeline_input, {
            #         "session_id": self.session_id,
            #         "user_id": self.user_id,
            #         "account_id": "12345"
            #     })

            state = State(
                    state_id=self.current_workflow_state_id,
                    config=state_config,
                    layer2_config=layer2_config,
                    memory=self.memory_manager,
                    tools_registry=self.agent_registry
                )
            result = await state.execute(input_data, {
                    "session_id": self.session_id,
                    "user_id": self.user_id,
                    "account_id": "12345"
                })
            await self.memory_manager.set("contextual", "output", result.outputs)
            self.execution_history.append(result)
            previous_state = self.current_workflow_state_id

            # Special handling for TTS states - start interrupt monitoring
            state_type = getattr(state_config, 'type', None)
            if state_type == "tts" and result.status == StatusType.SUCCESS:
                await self._start_tts_interrupt_monitoring(result)

            # Monitor for interrupt context after state execution
            await self._monitor_and_handle_interrupts()

            logger.info(
                "State execution completed",
                action="execute_step",
                output_data={
                    "previous_state": previous_state,
                    "next_state": self.current_workflow_state_id,
                    "result_status": result.status.value if hasattr(result.status, 'value') else str(result.status)
                },
                layer="state_management",
                metrics=result.meta.get("metrics", {})
            )

            # Update logger context for new state
            logger.update_context(state_id=self.current_workflow_state_id)

            return result

        except Exception as e:
            logger.error(
                "Error during state execution",
                action="execute_step",
                input_data={"current_state": self.current_workflow_state_id, "input": input_data},
                reason=str(e),
                layer="state_management"
            )
            raise

    async def executePipelineState(self, input_data: dict={}) -> StateOutput:
        """
        Executes a single pipeline state step using the current pipeline step ID.
        
        Args:
            input_data: Input data for the pipeline step.
        
        Returns:
            StateOutput: Result of the pipeline execution.
        
        Raises:
            WorkflowError: If the current pipeline step is not found or execution fails.
        """
        if not self.current_pipeline_step_id:
            raise WorkflowError("No current pipeline step set.")
        
        # Update logger context for current pipeline step
        logger.update_context(session_id=self.session_id, state_id=self.current_pipeline_step_id)

        logger.info(
            "Starting pipeline state execution",
            action="executePipelineState",
            input_data={"current_step": self.current_pipeline_step_id, "input": input_data},
            layer="state_management"
        )

        try:
            
            workFlowStateConfig = self.get_state(self.current_workflow_state_id)
            if not workFlowStateConfig:
                raise WorkflowError(f"Current workflow state '{self.current_workflow_state_id}' not found in workflow.")
            layer2 = self.get_layer2(workFlowStateConfig.layer2_id)
            if not layer2:
                logger.error(
                    f"Layer2 '{workFlowStateConfig.layer2_id}' not found in layer2_map during executePipelineState. Available Layer2s: {list(self.layer2_map.keys())}",
                    action="executePipelineState",
                    input_data={"current_state": self.current_workflow_state_id, "layer2_id": workFlowStateConfig.layer2_id},
                    layer="state_management"
                )
                raise WorkflowError(f"Layer2 for state '{self.current_workflow_state_id}' not found in workflow.")
            
            processToExecute = None
            pipelineFullState: PipelineStep = None
            for step in layer2.pipeline:
                if step.step == self.current_pipeline_step_id:
                    processToExecute = step.process
                    pipelineFullState = step
                    break

            if not processToExecute:
                raise WorkflowError(f"Pipeline step '{self.current_pipeline_step_id}' not found in Layer2 '{workFlowStateConfig.layer2_id}'.")
            
            if not pipelineFullState:
                raise WorkflowError(f"Pipeline step '{self.current_pipeline_step_id}' not found in Layer2 '{workFlowStateConfig.layer2_id}' pipeline.")

            pipeline_step: AbstractPipelineState = self.pipeline_state_map.get(processToExecute)
            if not pipeline_step:
                raise WorkflowError(f"Pipeline step '{self.current_pipeline_step_id}' not found in pipeline state map.")
            
            # Create an instance of the pipeline state class
            # Only pass interrupt_config to states that support it
            if processToExecute in ["interrupt_process"]:
                state = pipeline_step(
                    state_id=self.current_pipeline_step_id,
                    agent_registry=self.agent_registry,
                    session_id=self.session_id,
                    interrupt_config=self.interrupt_config
                )
            else:
                state = pipeline_step(
                    state_id=self.current_pipeline_step_id,
                    agent_registry=self.agent_registry,
                    session_id=self.session_id
                )

            process_input_data = {}
            print(f"[DEBUG] StateManager - Building input for step: {self.current_pipeline_step_id}")
            print(f"[DEBUG] StateManager - pipelineFullState.input: {pipelineFullState.input}")
            for key, value in pipelineFullState.input.items():
                memory_key = f"{self.session_id}_{self.current_workflow_state_id}_{value}"
                savedVal = await self.memory_manager.get(memory_key)
                print(f"[DEBUG] StateManager - Looking for key '{key}' -> memory_key '{memory_key}' -> value: {savedVal}")
                if savedVal:
                    process_input_data[key] = savedVal

            for key, value in input_data.items():
                process_input_data[key] = value
            print(f"[DEBUG] StateManager - Final process_input_data: {process_input_data}")
            # Execute the pipeline step
            result = await state.process(process_input_data, {
                "session_id": self.session_id,
                "user_id": self.user_id,
                "account_id": "12345",
                "memory_manager": self.memory_manager  # Pass memory manager through context
            })

            # Store TTS playback info for interrupt handling
            if processToExecute == "tts_process" and result.status.value == "success":
                self.current_tts_playback = result.outputs.get("audio_path")
                # Only start interrupt monitoring if not skipped (e.g., for acknowledgment TTS)
                if not self.skip_interrupt_monitoring:
                    print("[DEBUG] Calling _start_tts_interrupt_monitoring after TTS step.")
                    await self._start_tts_interrupt_monitoring(result)
                else:
                    print("[DEBUG] Skipping interrupt monitoring for acknowledgment TTS.")

            # await self.memory_manager.set("contextual", f"process_{processToExecute}_output", result.outputs)
            for key, value in result.outputs.items():
                await self.memory_manager.set("contextual", f"{self.session_id}_{self.current_workflow_state_id}_{key}", value)
            self.execution_history.append(result)

            # Monitor for interrupt context after pipeline step execution
            await self._monitor_and_handle_interrupts()

            return result
        except Exception as e:
            logger.error(
                "Error during pipeline state execution",
                action="executePipelineState",
                input_data={"current_step": self.current_pipeline_step_id, "input": input_data},
                reason=str(e),
                layer="state_management"
            )
            raise WorkflowError(f"Failed to execute pipeline state: {e}")

    async def handle_interrupt(self, audio_data: bytes,
                             current_tts_audio_path: Optional[str] = None,
                             playback_position: Optional[float] = None,
                             user_input: Optional[str] = None) -> StateOutput:
        """
        Handle an interrupt event using the InterruptManager.
        """
        try:
            # Delegate to the InterruptManager
            result = await self.interrupt_manager.handle_interrupt(
                audio_data=audio_data,
                current_tts_audio_path=current_tts_audio_path,
                playback_position=playback_position,
                user_input=user_input,
                state_manager=self
            )

            # Update local state based on result
            if result.status == StatusType.SUCCESS:
                self.interrupt_in_progress = True

            return result

        except Exception as e:
            logger.error(
                "Error handling interrupt",
                action="handle_interrupt",
                reason=str(e),
                layer="state_management"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Interrupt handling error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def transitionWorkflow(self, next_state_id: str):
        # TODO: Implement Adding Additional Layer2 steps
        # TODO: load context, intent & allowed/disallowed actions for every workflow
        try:
            state_config = self.get_state(self.current_workflow_state_id)
            if not state_config:
                raise WorkflowError(f"Current state '{self.current_workflow_state_id}' not found in workflow.")  
            # for transition in state_config.transitions:
            #     if transition.target == next_state_id:
            #         self.current_workflow_state_id = next_state_id
            #         return next_state_id
            self.current_workflow_state_id = next_state_id
            if self.current_workflow_state_id:
                current_state = self.get_state(self.current_workflow_state_id)
                if current_state and hasattr(current_state, 'layer2_id'):
                    layer2 = self.layer2_map.get(current_state.layer2_id)
                    if layer2 and layer2.pipeline:
                        self.current_pipeline_step_id = layer2.pipeline[0].step
                    else:
                        logger.warning(
                            f"Layer2 '{current_state.layer2_id}' not found in layer2_map during transition. Available Layer2s: {list(self.layer2_map.keys())}",
                            action="transitionWorkflow",
                            input_data={"current_state": self.current_workflow_state_id, "next_state": next_state_id},
                            layer="state_management"
                        )
            return next_state_id
        except Exception as e:
            logger.error(
                "Error during workflow transition",
                action="transitionWorkflow",
                input_data={"current_state": self.current_workflow_state_id, "next_state": next_state_id},
                reason=str(e),
                layer="state_management"
            )
            raise WorkflowError(f"Failed to transition workflow: {e}")
    

    async def transitionPipeline(self, next_state_id: str):
        try:
            state_config = self.get_state(self.current_workflow_state_id)
            if not state_config:
                raise WorkflowError(f"Current state '{self.current_workflow_state_id}' not found in workflow.")
            layer2 = self.layer2_map.get(state_config.layer2_id)
            if not layer2:
                raise WorkflowError(f"Layer2 for state '{self.current_workflow_state_id}' not found.")
            for step in layer2.pipeline:
                if step.step == next_state_id:
                    self.current_pipeline_step_id = next_state_id
                    return next_state_id
            raise WorkflowError(f"Pipeline step '{next_state_id}' not found in Layer2 '{state_config.layer2_id}'.")
        except Exception as e:
            logger.error(
                "Error during pipeline transition",
                action="transitionPipeline",
                input_data={"current_state": self.current_workflow_state_id, "next_step": next_state_id},
                reason=str(e),
                layer="state_management"
            )
            raise WorkflowError(f"Failed to transition pipeline: {e}")
        
    async def getWorkflow(self):  # TODO: -> WorkflowWrapper:
        """
        Returns the current workflow being executed.
        
        Returns:
            WorkflowWrapper: The current workflow instance.
        """
        if not self.workflow:
            raise WorkflowError("No workflow loaded.")
        return self.workflow
    
    async def getFullPipelineMap(self):  # TODO: -> List[Layer2]:
        """
        Returns a list of all Layer2 pipelines in the workflow.
        
        Returns:
            List[Layer2]: List of Layer2 pipeline definitions.
        """
        if not self.layer2_map:
            raise WorkflowError("No Layer2 pipelines loaded.")
        return list(self.layer2_map.values())
    
    async def getCurrentPipeline(self):  # TODO: -> Layer2:
        """
        Returns the current Layer2 pipeline for the current workflow state.
        
        Returns:
            Layer2: The current Layer2 pipeline definition.
        """
        if not self.current_workflow_state_id:
            raise WorkflowError("No current workflow state set.")
        state_config = self.get_state(self.current_workflow_state_id)
        if not state_config:
            raise WorkflowError(f"Current state '{self.current_workflow_state_id}' not found in workflow.")
        layer2 = self.get_layer2(state_config.layer2_id)
        if not layer2:
            raise WorkflowError(f"Layer2 for state '{self.current_workflow_state_id}' not found in workflow.")
        return layer2
    
    async def getCurrentWorkflowState(self) -> str:
        """
        Returns the ID of the current workflow state.
        
        Returns:
            str: The ID of the current workflow state.
        """
        if not self.current_workflow_state_id:
            raise WorkflowError("No current workflow state set.")
        return self.current_workflow_state_id
    
    async def getCurrentPipelineState(self) -> str:
        """
        Returns the ID of the current pipeline step.
        
        Returns:
            str: The ID of the current pipeline step.
        """
        if not self.current_pipeline_step_id:
            raise WorkflowError("No current pipeline step set.")
        return self.current_pipeline_step_id
    
    async def getProhibitedActions(self) -> List[str]:
        """
        Returns a list of prohibited actions for the current workflow state.
        
        Returns:
            List[str]: List of prohibited actions.
        """
        if not self.workflow.workflow:
            raise WorkflowError("No workflow loaded.")
        return self.workflow.workflow.prohibited_actions
    
    async def getAllowedActions(self) -> List[str]:
        """
        Returns a list of allowed actions for the current workflow state.
        
        Returns:
            List[str]: List of allowed actions.
        """
        if not self.workflow.workflow:
            raise WorkflowError("No workflow loaded.")
        return self.workflow.workflow.allowed_actions

    async def _update_session_metadata_after_execution(self, previous_state: str, result):
        """
        Update session metadata in contextual memory after state execution.
        """
        try:
            # Update current state
            await self.memory_manager.set("contextual", "current_state", self.current_state_id)

            # Track execution result status
            if hasattr(result, 'status'):
                status_str = result.status.value if hasattr(result.status, 'value') else str(result.status)
                if status_str in ['FAILURE', 'ERROR']:
                    # Track interrupts/errors
                    interrupts = await self.memory_manager.get("interrupts") or []
                    interrupts.append({
                        "state": previous_state,
                        "error": status_str,
                        "timestamp": datetime.now().isoformat()
                    })
                    await self.memory_manager.set("contextual", "interrupts", interrupts)

        except Exception as e:
            logger.error(
                "Error updating session metadata after execution",
                action="_update_session_metadata_after_execution",
                reason=str(e),
                layer="state_management"
            )

    async def handle_interrupt_event(self, interrupt_data: dict) -> bool:
        """
        Handle interrupt events using the InterruptManager.

        Args:
            interrupt_data: Dictionary containing interrupt information

        Returns:
            bool: True if interrupt was handled, False otherwise
        """
        try:
            # Delegate to the InterruptManager
            result = await self.interrupt_manager.handle_interrupt_event(interrupt_data)

            # Update local state based on result
            if result.status == StatusType.SUCCESS:
                self.interrupt_in_progress = True
                return True
            else:
                return False

        except Exception as e:
            logger.error(
                "Error handling interrupt event",
                action="handle_interrupt_event",
                reason=str(e),
                layer="state_management"
            )
            return False

    async def _deprecated_resume_after_interrupt(self) -> bool:
        """
        Resume execution after interrupt handling is complete.
        Handles both reversible and irreversible action flows.

        Returns:
            bool: True if resume was successful
        """
        try:
            # Get interrupt context
            interrupt_context = await self.memory_manager.get_interrupt_context()

            if not interrupt_context.get("detected"):
                logger.warning(
                    "No interrupt context found for resume",
                    action="resume_after_interrupt",
                    layer="state_management"
                )
                return False

            # Check if we should resume
            if not interrupt_context.get("resume_after_acknowledgment", True):
                logger.info(
                    "Interrupt context indicates no resume needed",
                    action="resume_after_interrupt",
                    layer="state_management"
                )
                return True

            # Get pre-interrupt state
            pre_interrupt_state = await self.memory_manager.get("contextual", "pre_interrupt_state")
            action_reversible = interrupt_context.get("action_reversible", True)
            should_resume_tts = interrupt_context.get("should_resume_tts", not action_reversible)

            logger.info(
                "Starting interrupt resumption flow",
                action="resume_after_interrupt",
                output_data={
                    "pre_interrupt_state": pre_interrupt_state,
                    "action_reversible": action_reversible,
                    "should_resume_tts": should_resume_tts
                },
                layer="state_management"
            )

            # Step 1: Speak acknowledgment message
            acknowledgment_message = interrupt_context.get("acknowledgment_message")
            if acknowledgment_message:
                try:
                    logger.info(
                        "Speaking acknowledgment message",
                        action="resume_after_interrupt",
                        output_data={"acknowledgment_message": acknowledgment_message},
                        layer="state_management"
                    )
                    tts_agent = self.agent_registry.getAgent("tts_agent")
                    await tts_agent.text_to_speech(acknowledgment_message)
                except Exception as e:
                    logger.error(
                        "Error speaking acknowledgment message",
                        action="resume_after_interrupt",
                        reason=str(e),
                        layer="state_management"
                    )

            # Step 2: Handle TTS resumption based on action reversibility
            # Always resume original TTS from pause point after acknowledgment
            try:
                logger.info(
                    "Resuming original TTS after interrupt (always)",
                    action="resume_after_interrupt",
                    layer="state_management"
                )

                # Get TTS playback controller and resume from pause point
                audio_path = interrupt_context.get("audio_path")
                playback_position = interrupt_context.get("playback_position", 0)

                if audio_path:
                    # Resume original TTS playback
                    from utils.audio_utils import TTSPlaybackController
                    playback_controller = TTSPlaybackController(
                        self.session_id,
                        interrupt_config=getattr(self, 'interrupt_config', None)
                    )
                    await playback_controller.resume_playback_from_position(audio_path, playback_position)

            except Exception as e:
                logger.error(
                    "Error resuming original TTS",
                    action="resume_after_interrupt",
                    reason=str(e),
                    layer="state_management"
                )

            # Step 3: Process queued user input based on action reversibility
            user_input_queued = interrupt_context.get("user_input_queued")
            if user_input_queued and user_input_queued != "[User interrupted during TTS]":
                if action_reversible:
                    # For reversible actions: queue for processing after TTS completion
                    logger.info(
                        "Queuing user input for after TTS completion (reversible action)",
                        action="resume_after_interrupt",
                        output_data={"user_input_queued": user_input_queued},
                        layer="state_management"
                    )
                    # Store in memory for processing after TTS completes
                    await self.memory_manager.set("contextual", "queued_user_input", user_input_queued)
                    await self.memory_manager.set("contextual", "process_queued_after_tts", True)
                else:
                    # For irreversible actions: no queuing, action already completed
                    logger.info(
                        "No queuing for irreversible action - action already completed",
                        action="resume_after_interrupt",
                        output_data={"user_input_queued": user_input_queued},
                        layer="state_management"
                    )

            # Step 4: Clean up interrupt context
            await self.memory_manager.clear_interrupt_context()

            # Step 5: Add resume event to history
            await self.memory_manager.add_interrupt_event(
                event_type="interrupt_resumed",
                details={
                    "resumed_state": pre_interrupt_state,
                    "action_reversible": action_reversible,
                    "should_resume_tts": should_resume_tts
                }
            )

            return True

        except Exception as e:
            logger.error(
                "Error resuming after interrupt",
                action="resume_after_interrupt",
                reason=str(e),
                layer="state_management"
            )
            return False

    async def _monitor_and_handle_interrupts(self):
        """
        Monitor for interrupt context in memory and handle interrupts proactively.
        This method is called after each state execution to check for interrupts.
        """
        try:
            # Delegate to the InterruptManager
            await self.interrupt_manager.monitor_and_handle_interrupts()

        except Exception as e:
            logger.error(
                "Error monitoring and handling interrupts",
                action="_monitor_and_handle_interrupts",
                reason=str(e),
                layer="state_management"
            )

    async def _start_tts_interrupt_monitoring(self, tts_result: StateOutput):
        """
        Start real-time interrupt monitoring using TTSInterruptMonitor.
        """
        try:
            # Delegate to the TTSInterruptMonitor
            await self.tts_monitor.start_tts_interrupt_monitoring(tts_result, state_manager=self)

        except Exception as e:
            logger.error(
                "Error starting TTS interrupt monitoring",
                action="_start_tts_interrupt_monitoring",
                reason=str(e),
                layer="state_management"
            )

    async def _run_tts_with_interrupt_monitoring(self, audio_path: str, interrupt_state: InterruptState, interrupt_config):
        """
        Run TTS playback with concurrent interrupt monitoring using TTSInterruptMonitor.
        """
        try:
            # Delegate to the TTSInterruptMonitor
            await self.tts_monitor.run_tts_with_interrupt_monitoring(
                audio_path, interrupt_state, interrupt_config, state_manager=self
            )

        except Exception as e:
            logger.error(
                "Error in TTS interrupt monitoring",
                action="_run_tts_with_interrupt_monitoring",
                reason=str(e),
                layer="state_management"
            )

    async def _process_audio_chunk_for_interrupt(self, audio_data: bytes, interrupt_state: InterruptState) -> bool:
        """
        Process audio chunk for interrupt detection using TTSInterruptMonitor.
        """
        try:
            # Delegate to the TTSInterruptMonitor
            return await self.tts_monitor.process_audio_chunk_for_interrupt(audio_data, interrupt_state)

        except Exception as e:
            logger.error(
                "Error processing audio chunk for interrupt",
                action="_process_audio_chunk_for_interrupt",
                reason=str(e),
                layer="state_management"
            )
            return False

    async def _handle_detected_interrupt(self, audio_data: bytes, current_tts_audio_path: str,
                                       playback_position: float, interrupt_state: InterruptState):
        """
        Handle detected interrupt using InterruptManager.
        """
        try:
            # Delegate to the InterruptManager
            return await self.interrupt_manager.handle_detected_interrupt(
                audio_data, current_tts_audio_path, playback_position, interrupt_state, state_manager=self
            )

        except Exception as e:
            logger.error(
                "Error handling detected interrupt",
                action="_handle_detected_interrupt",
                reason=str(e),
                layer="state_management"
            )
            return None

    async def _capture_and_transcribe_interrupt_audio(self, duration_sec: float = 5.0, sample_rate: int = 16000) -> str:
        """
        Capture and transcribe interrupt audio using TTSInterruptMonitor.
        """
        try:
            # Delegate to the TTSInterruptMonitor
            return await self.tts_monitor.capture_and_transcribe_interrupt_audio(duration_sec, sample_rate)

        except Exception as e:
            logger.error(f"Audio capture failed: {e}")
            return f"[Audio capture error: {str(e)}]"

    async def _process_queued_user_input_after_tts(self):
        """
        Process queued user input after TTS completion for reversible actions.
        This implements the proper workflow orchestration for interrupted reversible actions.
        """
        try:
            # Delegate to the InterruptManager
            processed = await self.interrupt_manager.process_queued_input_after_tts(self)

            if processed:
                logger.info(
                    "Queued user input processed after TTS completion",
                    action="_process_queued_user_input_after_tts",
                    layer="state_management"
                )

        except Exception as e:
            logger.error(
                "Error processing queued user input",
                action="_process_queued_user_input_after_tts",
                reason=str(e),
                layer="state_management"
            )

    async def _execute_queued_input_workflow(self, queued_input: str):
        """
        Execute a complete workflow for queued user input using InterruptManager.
        """
        try:
            # Delegate to the InterruptManager
            await self.interrupt_manager.execute_queued_input_workflow(queued_input, state_manager=self)

        except Exception as e:
            logger.error(
                "Error executing queued input workflow",
                action="_execute_queued_input_workflow",
                reason=str(e),
                layer="state_management"
            )

    def _get_target_state_for_intent(self, intent: str) -> str:
        """
        Map intent to target workflow state.
        This implements the intent-to-state routing logic.
        """
        intent_to_state_map = {
            "account_balance": "CheckBalance",
            "fund_transfer": "TransferFunds",
            "loan_application": "LoanApplication",
            "unknown": "Greeting"  # Fallback to greeting for unknown intents
        }

        return intent_to_state_map.get(intent, "Greeting")

    async def stop_interrupt_monitoring(self):
        """
        Stop interrupt monitoring if active.
        """
        try:
            if hasattr(self, 'interrupt_monitoring_task') and self.interrupt_monitoring_task:
                if not self.interrupt_monitoring_task.done():
                    self.interrupt_monitoring_task.cancel()
                    try:
                        await self.interrupt_monitoring_task
                    except asyncio.CancelledError:
                        pass

                logger.info(
                    "Interrupt monitoring stopped",
                    action="stop_interrupt_monitoring",
                    layer="state_management"
                )

        except Exception as e:
            logger.error(
                "Error stopping interrupt monitoring",
                action="stop_interrupt_monitoring",
                reason=str(e),
                layer="state_management"
            )

    async def process_interrupt_detection(self, audio_data: bytes, current_tts_audio_path: str = None,
                                        playback_position: float = 0.0) -> StateOutput:
        """
        Process interrupt detection using InterruptManager.
        """
        try:
            # Delegate to the InterruptManager
            return await self.interrupt_manager.process_interrupt_detection(
                audio_data, current_tts_audio_path, playback_position, state_manager=self
            )

        except Exception as e:
            logger.error(
                "Error processing interrupt detection",
                action="process_interrupt_detection",
                reason=str(e),
                layer="state_management"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Interrupt detection error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def _update_session_metadata_after_transition(self, previous_state: str, next_state: str):
        """
        Update session metadata in contextual memory after state transition.
        """
        try:
            from datetime import datetime

            # Update current state
            await self.memory_manager.set("contextual", "current_state", next_state)

            # Add to states_visited
            states_visited = await self.memory_manager.get("states_visited") or []
            if next_state not in states_visited:
                states_visited.append(next_state)
                await self.memory_manager.set("contextual", "states_visited", states_visited)

            # Check if this is a final state (no transitions)
            next_state_config = self.get_state(next_state)
            if next_state_config and (not next_state_config.transitions or len(next_state_config.transitions) == 0):
                await self.memory_manager.set("contextual", "outcome", "completed")
                await self.memory_manager.set("contextual", "final_action", f"reached_final_state_{next_state}")

            logger.debug(
                "Updated session metadata after transition",
                action="_update_session_metadata_after_transition",
                input_data={"previous_state": previous_state, "next_state": next_state},
                layer="state_management"
            )

        except Exception as e:
            logger.error(
                "Error updating session metadata after transition",
                action="_update_session_metadata_after_transition",
                reason=str(e),
                layer="state_management"
            )

    async def end_session_cleanup(self):
        """
        Clean up session and mark it as completed.
        """
        try:
            # Mark session as completed
            await self.memory_manager.set("contextual", "outcome", "completed")
            await self.memory_manager.set("contextual", "session_end_time", datetime.now().isoformat())

            # Set final action if not already set
            final_action = await self.memory_manager.get("final_action")
            if not final_action:
                await self.memory_manager.set("contextual", "final_action", "session_cleanup")

            logger.info(
                "Session cleanup completed",
                action="end_session_cleanup",
                layer="state_management"
            )

        except Exception as e:
            logger.error(
                "Error during session cleanup",
                action="end_session_cleanup",
                reason=str(e),
                layer="state_management"
            )

    async def example_memory_usage(self):
        """
        Example of how to use the memory manager for all memory layers in StateManager.
        """
        # --- Ephemeral memory: store temporary pipeline data ---
        await self.memory_manager.set("ephemeral", "transcribed_text", "Check my balance")
        # Retrieve ephemeral data
        transcribed = await self.memory_manager.get("transcribed_text")
        # Clear ephemeral after pipeline
        await self.memory_manager.clear_ephemeral()

        # --- Contextual memory: store session data and conversation ---
        await self.memory_manager.set("contextual", "user_message", "Check my balance")
        await self.memory_manager.set("contextual", "intent", "check_balance")
        await self.memory_manager.set("contextual", "slots", {"account_id": "12345"})
        # Store conversation turns
        conversation = await self.memory_manager.get("conversation") or []
        conversation.append({"role": "user", "text": "Check my balance 2"})
        conversation.append({"role": "ai", "text": "Sure, I'll check your balance 2"})
        await self.memory_manager.set("contextual", "conversation", conversation)
        # Save contextual memory to file for logging/debugging
        await self.memory_manager.contextual.save_to_file("contextual_memory_log.json")
        conversation.append({"role": "user", "text": "Check my balance 1"})
        conversation.append({"role": "ai", "text": "Sure, I'll check your balance 1"})
        await self.memory_manager.set("contextual", "conversation", conversation)
        # Clear contextual at end of session
        # await self.memory_manager.clear_contextual()
        # Log memory state for debugging
        logger.debug(
            "Current contextual memory state",
            action="example_memory_usage",
            output_data={
                "conversation": await self.memory_manager.contextual.get("conversation"),
                "all_contextual": await self.memory_manager.contextual.get_all()
            },
            layer="state_management"
        )

        # --- Persistent memory: store long-term user data ---
        await self.memory_manager.set("persistent", "validated_account_id", "12345")
        await self.memory_manager.set("persistent", "account_balance_history", {"2025-06-17": 5000})
        # Retrieve persistent data
        balance_history = await self.memory_manager.get("account_balance_history")
        # Explicit memory saving (e.g., user says "Remember my preferred language is English")
        await self.memory_manager.explicit_save("save_preference", {"preference": "language", "value": "en"})
        # Retrieve preference
        language = await self.memory_manager.get("language")


    # TODO: Implement handle_intent
    async def handle_intent(self, intent):
        """Example handler to show when to clear memory."""
        if intent == "goodbye":
            await self.end_session_cleanup()
            # Transition to end state, etc.

    async def check_and_persist_workflow_summary(self):
        """
        Checks if the workflow summary in persistent memory is up to date with the current workflow version.
        Only persists the summary if the version is different or summary does not exist.
        """
        if self.workflow and hasattr(self.workflow, 'workflow'):
            workflow_id = self.workflow.workflow.id
            new_version = self.workflow.workflow.version
            existing = await self.memory_manager.get(f"workflow_source_of_truth_{workflow_id}")
            existing_version = None
            if existing and isinstance(existing, dict):
                existing_version = existing.get("version")
            if existing_version != new_version:
                await self.memory_manager.persist_workflow_summary(self.workflow.workflow)
                logger.info(
                    f"Persisted workflow summary for {workflow_id} (version {new_version}) to persistent memory.",
                    action="persist_workflow_source_of_truth",
                    layer="state_management"
                )
            else:
                logger.info(
                    f"Workflow summary for {workflow_id} already up to date (version {new_version}). Skipping persist.",
                    action="persist_workflow_source_of_truth_skip",
                    layer="state_management"
                )
